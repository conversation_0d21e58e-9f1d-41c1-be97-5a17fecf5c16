# Copyright 2024 onwards Answer.AI, LightOn, and contributors
# License: Apache-2.0

"""
ROTALI (Rotary Attention with Learnable Inverse frequencies) implementation for FlexBERT.

This module implements the ROTALI attention mechanism which uses learnable parameters
to dynamically compute rotary embedding frequencies, providing adaptive positional encoding.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import warnings
from typing import Optional
import math

import bert_padding
from .configuration_bert import FlexBertConfig
from .attention import FlexBertAttentionBase
from .initialization import ModuleType, init_weights

try:
    from flash_attn import flash_attn_varlen_qkvpacked_func
    IMPL_USE_FLASH2 = True
except ImportError:
    IMPL_USE_FLASH2 = False


class FlexBertRotaliAttention(FlexBertAttentionBase):
    """
    ROTALI (Rotary Attention with Learnable Inverse frequencies) attention mechanism.
    
    This attention layer uses learnable parameters to dynamically compute rotary embedding
    frequencies, allowing for adaptive positional encoding that can learn optimal frequency
    patterns during training.
    
    Key features:
    - Learnable frequency computation via h1 linear layer
    - Dynamic rotary embeddings with logcumsumexp for frequency generation
    - Compatible with both padded and unpadded sequences
    - Optional Fourier layer integration
    """

    def __init__(self, config: FlexBertConfig, layer_id: Optional[int] = None):
        super().__init__(config=config, layer_id=layer_id)
        
        if config.hidden_size % config.num_attention_heads != 0:
            raise ValueError(
                f"The hidden size ({config.hidden_size}) is not a multiple of the number of attention "
                f"heads ({config.num_attention_heads})"
            )

        self.num_attention_heads = config.num_attention_heads
        self.attn_head_size = int(config.hidden_size / config.num_attention_heads)
        self.all_head_size = self.num_attention_heads * self.attn_head_size
        self.hidden_size = config.hidden_size
        self.p_dropout = config.attention_probs_dropout_prob
        
        # QKV projection
        self.Wqkv = nn.Linear(config.hidden_size, 3 * self.all_head_size, bias=config.attn_qkv_bias)
        # Output projection
        self.Wo = nn.Linear(config.hidden_size, config.hidden_size, bias=config.attn_out_bias)
        self.out_drop = (
            nn.Dropout(config.attn_out_dropout_prob) if config.attn_out_dropout_prob > 0.0 else nn.Identity()
        )
        
        # ROTALI-specific components
        # Learnable frequency computation layer
        self.h1 = nn.Linear(config.hidden_size, self.num_attention_heads)
        
        # Inverse frequency buffer for ROTALI computation
        inv_freq = torch.arange(1, self.hidden_size // (2 * self.num_attention_heads) + 1).float()
        self.register_buffer('inv_freq', inv_freq)
        
        # Flash Attention configuration
        self.use_fa2 = getattr(config, 'use_fa2', True) and IMPL_USE_FLASH2
        self.deterministic_fa2 = getattr(config, 'deterministic_fa2', False)
        self.use_sdpa_attn_mask = getattr(config, 'use_sdpa_attn_mask', True)
        
        # Warn if Flash Attention is not available
        if not IMPL_USE_FLASH2 and getattr(config, 'use_fa2', True):
            warnings.warn(
                "Flash Attention 2 not available, falling back to PyTorch SDPA. "
                "This may impact performance."
            )
            self.use_fa2 = False

    def _init_weights(self, reset_params: bool = False):
        """Initialize weights using the configuration's initialization method."""
        init_weights(
            self.config,
            self.Wqkv,
            layer_dim=self.config.hidden_size,
            layer_id=None,
            type_of_module=ModuleType.in_module,
        )
        init_weights(
            self.config,
            self.Wo,
            layer_dim=self.config.hidden_size,
            layer_id=self.layer_id,
            type_of_module=ModuleType.out_module,
        )
        init_weights(
            self.config,
            self.h1,
            layer_dim=self.config.hidden_size,
            layer_id=self.layer_id,
            type_of_module=ModuleType.in_module,
        )

    def rotate_half(self, x):
        """Rotates half the hidden dims of the input."""
        x1, x2 = x.chunk(2, dim=-1)
        return torch.cat((-x2, x1), dim=-1)

    def apply_rotary_pos_emb(self, q, k, cos, sin):
        """
        Apply rotary position embeddings to q and k tensors.
        
        Args:
            q: Query tensor of shape (batch_size, n_heads, seq_len, head_dim)
            k: Key tensor of shape (batch_size, n_heads, seq_len, head_dim)
            cos: Cosine rotary embeddings
            sin: Sine rotary embeddings
            
        Returns:
            q and k with rotary position embeddings applied
        """
        q_embed = (q * cos) + (self.rotate_half(q) * sin)
        k_embed = (k * cos) + (self.rotate_half(k) * sin)
        return q_embed, k_embed

    def compute_rotali_frequencies(self, hidden_states):
        """
        Compute ROTALI frequencies using learnable parameters.
        
        Args:
            hidden_states: Input tensor of shape (batch_size, seq_len, hidden_size)
            
        Returns:
            cos, sin: Cosine and sine embeddings for rotary position encoding
        """
        B, T, C = hidden_states.shape
        
        # Compute learnable frequency modulation
        # hhh = torch.log(1/(F.softplus(self.h1(hidden_states))+1))
        hhh = torch.log(1 / (F.softplus(self.h1(hidden_states)) + 1))
        
        # Reshape for head-wise computation
        z1 = hhh.view(B, T, self.num_attention_heads, 1).transpose(1, 2)  # (B, n_heads, T, 1)
        
        # Compute dynamic frequencies
        ddd = z1 * self.inv_freq.to(hidden_states.device).view(1, 1, 1, -1) * 2
        
        # Generate frequencies using logcumsumexp for numerical stability
        freqs = torch.exp(torch.logcumsumexp(ddd, dim=2))
        
        # Duplicate frequencies for cos/sin computation
        emb = torch.cat((freqs, freqs), dim=-1)
        
        # Compute cos and sin embeddings
        cos = torch.cos(emb)
        sin = torch.sin(emb)
        
        return cos, sin

    def forward(
        self,
        hidden_states: torch.Tensor,
        attn_mask: Optional[torch.Tensor] = None,
        **kwargs
    ) -> torch.Tensor:
        """
        Forward pass for ROTALI attention (padded version).

        Args:
            hidden_states: Input tensor of shape (batch_size, seq_len, hidden_size)
            attn_mask: Attention mask tensor

        Returns:
            attention: Output tensor of shape (batch_size, seq_len, hidden_size)
        """
        B, T, C = hidden_states.shape

        # Compute QKV projections
        qkv = self.Wqkv(hidden_states)  # (B, T, 3 * hidden_size)
        q, k, v = qkv.split(self.all_head_size, dim=2)

        # Reshape for multi-head attention
        q = q.view(B, T, self.num_attention_heads, self.attn_head_size).transpose(1, 2)  # (B, n_heads, T, head_dim)
        k = k.view(B, T, self.num_attention_heads, self.attn_head_size).transpose(1, 2)  # (B, n_heads, T, head_dim)
        v = v.view(B, T, self.num_attention_heads, self.attn_head_size).transpose(1, 2)  # (B, n_heads, T, head_dim)

        # Compute ROTALI frequencies
        cos, sin = self.compute_rotali_frequencies(hidden_states)

        # Apply rotary position embeddings
        q, k = self.apply_rotary_pos_emb(q, k, cos, sin)

        # Compute attention using Flash Attention or SDPA
        if self.use_fa2:
            # Prepare for Flash Attention
            qkv_fa = torch.stack([q, k, v], dim=2)  # (B, n_heads, 3, T, head_dim)
            qkv_fa = qkv_fa.transpose(1, 3).contiguous()  # (B, T, 3, n_heads, head_dim)
            qkv_fa = qkv_fa.view(-1, 3, self.num_attention_heads, self.attn_head_size)  # (B*T, 3, n_heads, head_dim)

            # Create cumulative sequence lengths for Flash Attention
            cu_seqlens = torch.arange(0, (B + 1) * T, step=T, dtype=torch.int32, device=hidden_states.device)

            attn = flash_attn_varlen_qkvpacked_func(
                qkv_fa,
                cu_seqlens=cu_seqlens,
                max_seqlen=T,
                dropout_p=self.p_dropout,
                deterministic=self.deterministic_fa2,
            )
            attn = attn.view(B, T, self.num_attention_heads, self.attn_head_size)
            attn = attn.view(B, T, self.all_head_size)
        else:
            # Use PyTorch SDPA
            attn = F.scaled_dot_product_attention(
                q, k, v,
                attn_mask=attn_mask,
                dropout_p=self.p_dropout,
                is_causal=False
            )
            attn = attn.transpose(1, 2).contiguous().view(B, T, self.all_head_size)

        # Apply output projection
        attn = self.Wo(attn)
        return self.out_drop(attn)


class FlexBertUnpadRotaliAttention(FlexBertAttentionBase):
    """
    ROTALI attention for unpadded sequences.

    This version is optimized for unpadded sequences and integrates with the
    FlexBERT unpadding infrastructure for improved efficiency.
    """

    def __init__(self, config: FlexBertConfig, layer_id: Optional[int] = None):
        super().__init__(config=config, layer_id=layer_id)

        if config.hidden_size % config.num_attention_heads != 0:
            raise ValueError(
                f"The hidden size ({config.hidden_size}) is not a multiple of the number of attention "
                f"heads ({config.num_attention_heads})"
            )

        self.num_attention_heads = config.num_attention_heads
        self.attn_head_size = int(config.hidden_size / config.num_attention_heads)
        self.all_head_size = self.num_attention_heads * self.attn_head_size
        self.hidden_size = config.hidden_size
        self.p_dropout = config.attention_probs_dropout_prob

        # QKV projection
        self.Wqkv = nn.Linear(config.hidden_size, 3 * self.all_head_size, bias=config.attn_qkv_bias)
        # Output projection
        self.Wo = nn.Linear(config.hidden_size, config.hidden_size, bias=config.attn_out_bias)
        self.out_drop = (
            nn.Dropout(config.attn_out_dropout_prob) if config.attn_out_dropout_prob > 0.0 else nn.Identity()
        )

        # ROTALI-specific components
        self.h1 = nn.Linear(config.hidden_size, self.num_attention_heads)

        # Inverse frequency buffer
        inv_freq = torch.arange(1, self.hidden_size // (2 * self.num_attention_heads) + 1).float()
        self.register_buffer('inv_freq', inv_freq)

        # Flash Attention configuration
        self.use_fa2 = getattr(config, 'use_fa2', True) and IMPL_USE_FLASH2
        self.deterministic_fa2 = getattr(config, 'deterministic_fa2', False)
        self.use_sdpa_attn_mask = getattr(config, 'use_sdpa_attn_mask', True)

        if not IMPL_USE_FLASH2 and getattr(config, 'use_fa2', True):
            warnings.warn(
                "Flash Attention 2 not available, falling back to PyTorch SDPA. "
                "This may impact performance for unpadded sequences."
            )
            self.use_fa2 = False

    def _init_weights(self, reset_params: bool = False):
        """Initialize weights using the configuration's initialization method."""
        init_weights(self.config, self.Wqkv, layer_dim=self.config.hidden_size, layer_id=None, type_of_module=ModuleType.in_module)
        init_weights(self.config, self.Wo, layer_dim=self.config.hidden_size, layer_id=self.layer_id, type_of_module=ModuleType.out_module)
        init_weights(self.config, self.h1, layer_dim=self.config.hidden_size, layer_id=self.layer_id, type_of_module=ModuleType.in_module)

    def rotate_half(self, x):
        """Rotates half the hidden dims of the input."""
        x1, x2 = x.chunk(2, dim=-1)
        return torch.cat((-x2, x1), dim=-1)

    def apply_rotary_pos_emb(self, q, k, cos, sin):
        """Apply rotary position embeddings to q and k tensors."""
        q_embed = (q * cos) + (self.rotate_half(q) * sin)
        k_embed = (k * cos) + (self.rotate_half(k) * sin)
        return q_embed, k_embed

    def compute_rotali_frequencies(self, hidden_states):
        """
        Compute ROTALI frequencies using learnable parameters (padded version).

        Args:
            hidden_states: Input tensor of shape (batch_size, seq_len, hidden_size)

        Returns:
            cos, sin: Cosine and sine embeddings for rotary position encoding
        """
        B, T, C = hidden_states.shape

        # Compute learnable frequency modulation
        hhh = torch.log(1 / (F.softplus(self.h1(hidden_states)) + 1))

        # Reshape for head-wise computation
        z1 = hhh.view(B, T, self.num_attention_heads, 1).transpose(1, 2)  # (B, n_heads, T, 1)

        # Compute dynamic frequencies
        ddd = z1 * self.inv_freq.to(hidden_states.device).view(1, 1, 1, -1) * 2

        # Generate frequencies using logcumsumexp for numerical stability
        freqs = torch.exp(torch.logcumsumexp(ddd, dim=2))

        # Duplicate frequencies for cos/sin computation
        emb = torch.cat((freqs, freqs), dim=-1)

        # Compute cos and sin embeddings
        cos = torch.cos(emb)
        sin = torch.sin(emb)

        return cos, sin

    def compute_rotali_frequencies_unpad(self, hidden_states, cu_seqlens, max_seqlen):
        """
        Compute ROTALI frequencies for unpadded sequences.

        Args:
            hidden_states: Unpadded tensor of shape (total_nnz, hidden_size)
            cu_seqlens: Cumulative sequence lengths
            max_seqlen: Maximum sequence length in the batch

        Returns:
            cos, sin: Cosine and sine embeddings for rotary position encoding
        """
        total_nnz, C = hidden_states.shape

        # Compute learnable frequency modulation
        hhh = torch.log(1 / (F.softplus(self.h1(hidden_states)) + 1))  # (total_nnz, n_heads)

        # Pad the sequences to compute frequencies properly
        padded_hhh = bert_padding.pad_input(
            hhh,
            torch.arange(total_nnz, device=hidden_states.device),
            cu_seqlens.shape[0] - 1,
            max_seqlen
        )  # (batch_size, max_seqlen, n_heads)

        B, T, _ = padded_hhh.shape

        # Reshape for head-wise computation
        z1 = padded_hhh.view(B, T, self.num_attention_heads, 1).transpose(1, 2)  # (B, n_heads, T, 1)

        # Compute dynamic frequencies
        ddd = z1 * self.inv_freq.to(hidden_states.device).view(1, 1, 1, -1) * 2

        # Generate frequencies using logcumsumexp for numerical stability
        freqs = torch.exp(torch.logcumsumexp(ddd, dim=2))

        # Duplicate frequencies for cos/sin computation
        emb = torch.cat((freqs, freqs), dim=-1)

        # Compute cos and sin embeddings
        cos = torch.cos(emb)
        sin = torch.sin(emb)

        # Unpad the results
        cos_unpad = bert_padding.unpad_input_only(cos.transpose(1, 2), cu_seqlens != 0)  # Simplified mask
        sin_unpad = bert_padding.unpad_input_only(sin.transpose(1, 2), cu_seqlens != 0)

        return cos_unpad, sin_unpad

    def forward(
        self,
        hidden_states: torch.Tensor,
        cu_seqlens: torch.Tensor,
        max_seqlen: int,
        indices: torch.Tensor,
        attn_mask: torch.Tensor,
    ) -> torch.Tensor:
        """
        Forward pass for ROTALI attention (unpadded version).

        Args:
            hidden_states: Unpadded tensor of shape (total_nnz, hidden_size)
            cu_seqlens: Cumulative sequence lengths
            max_seqlen: Maximum sequence length in the batch
            indices: Indices for unpadding
            attn_mask: Attention mask

        Returns:
            attention: Output tensor of shape (total_nnz, hidden_size)
        """
        bs, dim = hidden_states.shape

        # Compute QKV projections
        qkv = self.Wqkv(hidden_states)

        # Always use SDPA path for ROTALI with unpadded sequences
        # Flash Attention with rotary embeddings on unpadded sequences is complex

        # Fallback to padded attention with SDPA
        qkv = bert_padding.pad_input(qkv, indices, cu_seqlens.shape[0] - 1, max_seqlen)
        unpad_bs, seqlen, _ = qkv.shape

        qkv = qkv.view(unpad_bs, -1, 3, self.num_attention_heads, self.attn_head_size)
        q, k, v = qkv.transpose(3, 1).unbind(dim=2)  # b h s d

        # Compute ROTALI frequencies for padded version
        padded_hidden = bert_padding.pad_input(hidden_states, indices, cu_seqlens.shape[0] - 1, max_seqlen)
        cos, sin = self.compute_rotali_frequencies(padded_hidden)

        # Apply rotary embeddings
        q, k = self.apply_rotary_pos_emb(q, k, cos, sin)

        attn = F.scaled_dot_product_attention(
            q, k, v,
            dropout_p=self.p_dropout,
            attn_mask=attn_mask[:, None, None, :seqlen].to(torch.bool).expand(unpad_bs, 1, seqlen, seqlen)
            if self.use_sdpa_attn_mask else None,
        )
        attn = attn.transpose(1, 2).view(unpad_bs, -1, self.hidden_size)
        attn = bert_padding.unpad_input_only(attn, torch.squeeze(attn_mask) == 1)

        return self.out_drop(self.Wo(attn))
