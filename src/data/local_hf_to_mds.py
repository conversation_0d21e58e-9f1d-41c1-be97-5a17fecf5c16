import tqdm
import argparse
import os
import json
import multiprocessing
import subprocess
import warnings
from datasets import load_dataset, load_from_disk, interleave_datasets, concatenate_datasets
from transformers import set_seed
from streaming import MDSWriter, StreamingDataset
from streaming.base.util import clean_stale_shared_memory
from data_utils import MDS_COLS_TEXT
from torch.utils.data import DataLoader

# It disables cache files creation on source dataset path
# as it's on sshfs storage server and probably gonna run in to permission issues
from datasets import disable_caching
disable_caching()

set_seed(11111111)

# Function to clean up shared memory segments
def cleanup_shared_memory():
    try:
        # First try the built-in cleanup function silently
        clean_stale_shared_memory()
    except Exception:
        try:
            # Fallback to manual cleanup using system commands
            # Check if /dev/shm exists
            if os.path.exists("/dev/shm"):
                # Remove cache usage segments if they exist
                if any(f.endswith("cache_usage") for f in os.listdir("/dev/shm") if os.path.isfile(os.path.join("/dev/shm", f))):
                    subprocess.run(
                        "rm -f /dev/shm/*cache_usage 2>/dev/null",
                        shell=True
                    )

                # Remove other streaming segments if they exist
                if any(f.startswith("000000_") for f in os.listdir("/dev/shm") if os.path.isfile(os.path.join("/dev/shm", f))):
                    subprocess.run(
                        "rm -f /dev/shm/000000_* 2>/dev/null",
                        shell=True
                    )
        except Exception:
            # Silently continue if cleanup fails
            pass

def convert_hf_to_mds(output_path: str,
                     dataset_info: dict,
                     num_proc: int = None,
                     shuffle_seed: int = 42,
                     batch_size: int = 1,
                     compression: str = None):
    """Convert multiple HuggingFace datasets to a single MDS format.

    Args:
        output_path: Local path to save the MDS dataset
        dataset_info: Dictionary containing information about datasets to convert
            Each entry should have:
                - path: Local path to the dataset
                - text_column: Name of the column containing text to convert
                - split: Dataset split to convert (e.g., 'train', 'validation')
                - config: (Optional) Dataset configuration name
        num_proc: Number of processes to use for conversion
        shuffle_seed: Random seed for shuffling the combined dataset (default: 42)
        batch_size: Batch size for processing data (default: 1)
            - Higher values can improve throughput with high-bandwidth connections
            - Values of 64-256 often work well for network storage
            - For very large datasets on fast networks, try 512-1024
    """
    # First check if output directory exists
    assert not os.path.exists(output_path), (
        f"\nOutput directory '{output_path}' already exists.\n"
        f"Please specify a different output path or remove the existing directory."
    )

    print(f"Converting multiple datasets to MDS format:")
    print(f"- Output: {output_path}")

    # Set number of workers if not specified
    if num_proc is None:
        num_proc = max(1, multiprocessing.cpu_count() // 2)
    print(f"- Using {num_proc} workers")

    # Load all datasets
    datasets_list = []

    for idx, dataset_config in enumerate(dataset_info):
        path = dataset_config["path"]
        text_column = dataset_config["text_column"]
        split = dataset_config.get("split")
        config = dataset_config.get("config")
        num_records = dataset_config.get("num_records")

        print(f"\nLoading dataset {idx+1}/{len(dataset_info)}:")
        print(f"- Path: {path}")
        print(f"- Text column: {text_column}")
        print(f"- Split: {split}")
        print(f"- Config: {config}")
        if num_records:
            print(f"- Limiting to {num_records} records (for testing)")

        # Check if path exists
        if not os.path.exists(path):
            print(f"WARNING: Path '{path}' does not exist or is not accessible!")
            print(f"Skipping dataset {idx+1}")
            continue

        # Load the dataset with appropriate configuration
        try:
            print(f"Attempting to load dataset {idx+1}...")
            # First try to load as a regular HuggingFace dataset
            try:
                if config is not None and split:
                    print(f"Loading with config '{config}' and split '{split}'")
                    dataset = load_dataset(path, config, split=split)
                elif config is not None:
                    print(f"Loading with config '{config}'")
                    dataset = load_dataset(path, config)
                elif split is not None:
                    print(f"Loading with split '{split}'")
                    dataset = load_dataset(path, split=split)
                else:
                    print("Loading without config or split")
                    dataset = load_dataset(path)
                print(f"Successfully loaded dataset using load_dataset()")
            except ValueError as e:
                # If that fails, try to load as a local disk dataset
                if "Please use `load_from_disk` instead" in str(e):
                    print(f"Detected local disk dataset at {path}, using load_from_disk")
                    try:
                        dataset = load_from_disk(path)
                        print(f"Successfully loaded dataset using load_from_disk()")
                        # If split is specified and the dataset is a DatasetDict, select the split
                        if split is not None and hasattr(dataset, 'keys') and split in dataset:
                            print(f"Selecting split '{split}' from DatasetDict")
                            dataset = dataset[split]
                            print(f"Successfully selected split '{split}'")
                    except Exception as disk_error:
                        print(f"Failed to load dataset from {path}: {disk_error}")
                        print(f"Skipping dataset {idx+1}")
                        continue
                else:
                    print(f"Error loading dataset: {e}")
                    print(f"Skipping dataset {idx+1}")
                    continue
            except Exception as load_error:
                print(f"Error loading dataset: {load_error}")
                print(f"Skipping dataset {idx+1}")
                continue

            print(f"Dataset {idx+1} loaded successfully with {len(dataset)} records")
        except Exception as e:
            print(f"Unexpected error loading dataset {idx+1}: {e}")
            print(f"Skipping dataset {idx+1}")
            continue

        # Limit the number of records if specified (for testing purposes)
        if num_records:
            if isinstance(dataset, dict):
                # Handle case where dataset is a DatasetDict
                for split_name in dataset:
                    dataset[split_name] = dataset[split_name].select(range(min(num_records, len(dataset[split_name]))))
                    print(f"  - Limited split '{split_name}' to {len(dataset[split_name])} records")
            else:
                # Handle case where dataset is a regular Dataset
                dataset = dataset.select(range(min(num_records, len(dataset))))
                print(f"  - Limited to {len(dataset)} records")

        # Rename the text column to 'text' if it's not already named 'text'
        if text_column != "text":
            column_names = dataset.column_names
            if isinstance(column_names, list):
                if text_column in column_names:
                    dataset = dataset.rename_column(text_column, "text")
                else:
                    raise ValueError(f"Text column '{text_column}' not found in dataset columns: {column_names}")
            else:
                # Handle case where dataset is a DatasetDict
                for split_name in dataset:
                    if text_column in dataset[split_name].column_names:
                        dataset[split_name] = dataset[split_name].rename_column(text_column, "text")
                    else:
                        raise ValueError(f"Text column '{text_column}' not found in dataset columns for split {split_name}: {dataset[split_name].column_names}")

        # Keep only the 'text' column and add an 'id' column if it doesn't exist
        if isinstance(dataset.column_names, list):
            # Remove all columns except 'text'
            columns_to_remove = [col for col in dataset.column_names if col != "text"]
            if columns_to_remove:
                dataset = dataset.remove_columns(columns_to_remove)

            # Add 'id' column if it doesn't exist
            if "id" not in dataset.column_names:
                dataset = dataset.add_column("id", [f"dataset_{idx}_{i}" for i in range(len(dataset))])
        else:
            # Handle case where dataset is a DatasetDict
            for split_name in dataset:
                # Remove all columns except 'text'
                columns_to_remove = [col for col in dataset[split_name].column_names if col != "text"]
                if columns_to_remove:
                    dataset[split_name] = dataset[split_name].remove_columns(columns_to_remove)

                # Add 'id' column if it doesn't exist
                if "id" not in dataset[split_name].column_names:
                    dataset[split_name] = dataset[split_name].add_column(
                        "id", [f"dataset_{idx}_{split_name}_{i}" for i in range(len(dataset[split_name]))]
                    )

        datasets_list.append(dataset)

    # Combine all datasets
    print(f"\nPreparing to combine datasets. Number of datasets loaded: {len(datasets_list)}")
    for i, ds in enumerate(datasets_list):
        if isinstance(ds, dict):
            print(f"Dataset {i+1} is a DatasetDict with splits: {list(ds.keys())}")
            for split_name in ds:
                print(f"  - Split '{split_name}' has {len(ds[split_name])} records")
        else:
            print(f"Dataset {i+1} is a Dataset with {len(ds)} records")

    if len(datasets_list) > 1:
        print("\nCombining all datasets...")
        if isinstance(datasets_list[0], dict):
            # Handle case where datasets are DatasetDicts
            combined_dataset = {}
            for split_name in datasets_list[0]:
                split_datasets = [ds[split_name] for ds in datasets_list if split_name in ds]
                print(f"Found {len(split_datasets)} datasets with split '{split_name}'")
                if split_datasets:
                    try:
                        # Concatenate datasets for this split
                        print(f"Concatenating datasets for split '{split_name}'...")
                        concatenated = concatenate_datasets(split_datasets)
                        # Shuffle the concatenated dataset for better mixing
                        print(f"Shuffling combined dataset for split '{split_name}'...")
                        combined_dataset[split_name] = concatenated.shuffle(seed=shuffle_seed)
                        print(f"Successfully combined {len(split_datasets)} datasets for split '{split_name}' with {len(combined_dataset[split_name])} total records")
                    except Exception as e:
                        print(f"Error combining datasets for split '{split_name}': {e}")
                        # Fallback to using just the first dataset if concatenation fails
                        print(f"Falling back to using only the first dataset for split '{split_name}'")
                        combined_dataset[split_name] = split_datasets[0]
        else:
            try:
                # Concatenate all datasets
                print(f"\nConcatenating {len(datasets_list)} datasets...")
                for i, ds in enumerate(datasets_list):
                    print(f"Dataset {i+1} has {len(ds)} records and columns: {ds.column_names}")

                # Use concatenate_datasets instead of interleave_datasets to avoid cache file issues
                concatenated = concatenate_datasets(datasets_list)
                print(f"Concatenation successful. Combined dataset has {len(concatenated)} records")

                # Shuffle the concatenated dataset for better mixing
                print("\nShuffling combined dataset for better language mixing...")
                combined_dataset = concatenated.shuffle(seed=shuffle_seed)
                print(f"Shuffling successful. Final dataset has {len(combined_dataset)} records")
            except Exception as e:
                print(f"Error concatenating datasets: {e}")
                # Fallback to using just the first dataset if concatenation fails
                print("Falling back to using only the first dataset")
                combined_dataset = datasets_list[0]
    else:
        print("\nOnly one dataset loaded, no combination needed.")
        combined_dataset = datasets_list[0]

    # Create DataLoader for parallel processing
    if isinstance(combined_dataset, dict):
        # Handle case where combined_dataset is a DatasetDict
        total_instances = 0
        for split_name, split_dataset in combined_dataset.items():
            print(f"\nProcessing split: {split_name}")
            split_output_path = os.path.join(output_path, split_name)
            os.makedirs(split_output_path)

            dataloader = DataLoader(
                split_dataset,
                batch_size=batch_size,  # Use the specified batch size
                num_workers=num_proc,
                prefetch_factor=2 if batch_size <= 256 else 1  # Reduce prefetch for large batches
            )

            # Convert to MDS format
            print(f"Writing split '{split_name}' to MDS format...")
            with MDSWriter(out=split_output_path,
                          columns=MDS_COLS_TEXT,
                          compression=compression) as writer:
                for batch in tqdm.tqdm(dataloader):
                    if batch_size == 1:
                        # For batch_size=1, take the first item
                        item = {k: v[0] for k, v in batch.items()}
                        writer.write(item)
                    else:
                        # For larger batches, write each item in the batch
                        for i in range(len(next(iter(batch.values())))):
                            item = {k: v[i] for k, v in batch.items()}
                            writer.write(item)

            # Verify the conversion
            print(f"Verifying conversion for split '{split_name}'...")
            # Clean up any stale shared memory before verification
            cleanup_shared_memory()

            try:
                dataset = StreamingDataset(local=split_output_path,
                                         shuffle=False,
                                         split=None,
                                         batch_size=1)
                num_instances = len(dataset)
            except Exception:
                # Silently handle the error and estimate instances from dataloader
                print(f"Note: Using dataloader to estimate number of instances for split '{split_name}'...")
                num_instances = len(list(tqdm.tqdm(dataloader)))

            total_instances += num_instances

            # Get dataset size on disk
            split_size = sum(
                os.path.getsize(os.path.join(dirpath, filename))
                for dirpath, _, filenames in os.walk(split_output_path)
                for filename in filenames
            )

            print(f"\nSplit '{split_name}' conversion completed:")
            print(f"- Number of instances: {num_instances:,}")
            print(f"- Dataset size: {split_size / (1024**3):.2f} GB")

        # Get total dataset size on disk
        total_size = sum(
            os.path.getsize(os.path.join(dirpath, filename))
            for dirpath, _, filenames in os.walk(output_path)
            for filename in filenames
        )

        print("\nAll splits conversion completed successfully:")
        print(f"- Total number of instances: {total_instances:,}")
        print(f"- Total dataset size: {total_size / (1024**3):.2f} GB")
        print(f"- Output path: {output_path}")

        return total_instances, total_size
    else:
        # Create output directory if it doesn't exist
        os.makedirs(output_path)

        dataloader = DataLoader(
            combined_dataset,
            batch_size=batch_size,  # Use the specified batch size
            num_workers=num_proc,
            prefetch_factor=2 if batch_size <= 256 else 1  # Reduce prefetch for large batches
        )

        # Convert to MDS format
        print("\nWriting to MDS format...")
        with MDSWriter(out=output_path,
                      columns=MDS_COLS_TEXT,
                      compression=compression) as writer:
            for batch in tqdm.tqdm(dataloader):
                if batch_size == 1:
                    # For batch_size=1, take the first item
                    item = {k: v[0] for k, v in batch.items()}
                    writer.write(item)
                else:
                    # For larger batches, write each item in the batch
                    for i in range(len(next(iter(batch.values())))):
                        item = {k: v[i] for k, v in batch.items()}
                        writer.write(item)

        # Verify the conversion
        print("Verifying conversion...")
        # Clean up any stale shared memory before verification
        cleanup_shared_memory()

        try:
            dataset = StreamingDataset(local=output_path,
                                     shuffle=False,
                                     split=None,
                                     batch_size=1)
            num_instances = len(dataset)
        except Exception:
            # Silently handle the error and estimate instances from dataloader
            print("Note: Using dataloader to estimate number of instances...")
            num_instances = len(list(tqdm.tqdm(dataloader)))

        # Get dataset size on disk
        total_size = sum(
            os.path.getsize(os.path.join(dirpath, filename))
            for dirpath, _, filenames in os.walk(output_path)
            for filename in filenames
        )

        print("\nConversion completed successfully:")
        print(f"- Number of instances: {num_instances:,}")
        print(f"- Dataset size: {total_size / (1024**3):.2f} GB")
        print(f"- Output path: {output_path}")

        return num_instances, total_size

def convert_single_hf_to_mds(output_path: str,
                           repo_name: str,
                           split_name: str = None,
                           config_name: str = None,
                           text_column: str = "text",
                           num_proc: int = None,
                           shuffle_seed: int = 42,
                           batch_size: int = 1,
                           compression: str = None):
    """Convert a single HuggingFace dataset to MDS format.

    Args:
        output_path: Local path to save the MDS dataset
        repo_name: HuggingFace dataset repository name
        split_name: Dataset split to convert (e.g., 'train', 'validation')
        config_name: Dataset configuration name
        text_column: Name of the column containing text to convert
        num_proc: Number of processes to use for conversion
        shuffle_seed: Random seed for shuffling the dataset (default: 42)
        batch_size: Batch size for processing data (default: 1)
    """
    dataset_info = [{
        "path": repo_name,
        "text_column": text_column,
        "split": split_name,
        "config": config_name
    }]

    return convert_hf_to_mds(
        output_path=output_path,
        dataset_info=dataset_info,
        num_proc=num_proc,
        shuffle_seed=shuffle_seed,
        batch_size=batch_size,
        cpmoression=compression
    )

def main():
    # Clean up shared memory at the start
    cleanup_shared_memory()

    parser = argparse.ArgumentParser(
        description="Convert HuggingFace dataset(s) to MDS format locally"
    )
    parser.add_argument(
        "-o",
        "--output_path",
        type=str,
        required=True,
        help="Local path to save the MDS dataset"
    )

    # Arguments for single dataset conversion (backward compatibility)
    parser.add_argument(
        "-r",
        "--repo_name",
        type=str,
        required=False,
        help="HuggingFace dataset repository name (for single dataset conversion)"
    )
    parser.add_argument(
        "-s",
        "--split",
        type=str,
        required=False,
        help="Dataset split to convert (e.g., 'train', 'validation')"
    )
    parser.add_argument(
        "-c",
        "--config",
        type=str,
        required=False,
        help="Dataset configuration name"
    )
    parser.add_argument(
        "-t",
        "--text_column",
        type=str,
        default="text",
        help="Name of the column containing text to convert (default: 'text')"
    )

    # Arguments for multiple datasets conversion
    parser.add_argument(
        "-d",
        "--datasets_json",
        type=str,
        required=False,
        help="Path to JSON file containing dataset information"
    )

    parser.add_argument(
        "-p",
        "--num_proc",
        type=int,
        default=None,
        help="Number of processes to use (default: num_cpu_cores // 2)"
    )

    parser.add_argument(
        "--shuffle_seed",
        type=int,
        default=42,
        help="Random seed for shuffling the combined dataset (default: 42)"
    )

    parser.add_argument(
        "-b",
        "--batch_size",
        type=int,
        default=1,
        help="Batch size for processing data (default: 1). Higher values (64-256) can improve throughput with high-bandwidth connections."
    )
    
    parser.add_argument(
        "-cmp",
        "--compression",
        type=str,
        default=None,
        help="MDSWriter compression type (default: None). Options: 'zstd', 'lz4', 'gzip', 'bz2')"
    )

    args = parser.parse_args()

    # Check if we're using the multiple datasets mode or single dataset mode
    if args.datasets_json:
        # Multiple datasets mode
        with open(args.datasets_json, 'r') as f:
            dataset_info = json.load(f)

        convert_hf_to_mds(
            output_path=args.output_path,
            dataset_info=dataset_info,
            num_proc=args.num_proc,
            shuffle_seed=args.shuffle_seed,
            batch_size=args.batch_size,
            compression=args.compression
        )
    elif args.repo_name:
        # Single dataset mode (backward compatibility)
        convert_single_hf_to_mds(
            output_path=args.output_path,
            repo_name=args.repo_name,
            split_name=args.split,
            config_name=args.config,
            text_column=args.text_column,
            num_proc=args.num_proc,
            shuffle_seed=args.shuffle_seed,
            batch_size=args.batch_size,
            compression=args.compression
        )
    else:
        parser.error("Either --repo_name or --datasets_json must be provided")

if __name__ == "__main__":
    main()
