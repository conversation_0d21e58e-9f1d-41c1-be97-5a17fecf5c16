#!/usr/bin/env python3
"""
Test script to verify ROTALI parameters receive gradients during training.

This script specifically tests that the h1 linear layers in ROTALI attention
receive gradients, which was the issue causing the DDP error.
"""

import sys
import os
import torch
import torch.nn as nn

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_rotali_gradients():
    """Test that ROTALI parameters receive gradients."""
    print("Testing ROTALI gradient flow...")
    
    try:
        from src.bert_layers.configuration_bert import FlexBertConfig
        from src.bert_layers.rotali_attention import FlexBertRotaliAttention
        
        config = FlexBertConfig(
            hidden_size=768,
            num_attention_heads=12,
            use_rotali=True,
            use_fa2=False,  # Disable Flash Attention for testing
            attention_probs_dropout_prob=0.0
        )
        
        attention = FlexBertRotaliAttention(config, layer_id=0)
        
        # Create test input
        batch_size, seq_len = 2, 16
        hidden_states = torch.randn(batch_size, seq_len, config.hidden_size, requires_grad=True)
        
        # Forward pass
        output = attention(hidden_states)
        
        # Create a simple loss (sum of outputs)
        loss = output.sum()
        
        # Backward pass
        loss.backward()
        
        # Check if h1 parameters have gradients
        h1_has_grad = attention.h1.weight.grad is not None
        h1_grad_norm = attention.h1.weight.grad.norm().item() if h1_has_grad else 0.0
        
        print(f"✓ ROTALI h1 layer has gradients: {h1_has_grad}")
        print(f"✓ h1 gradient norm: {h1_grad_norm:.6f}")
        
        # Check other parameters too
        qkv_has_grad = attention.Wqkv.weight.grad is not None
        wo_has_grad = attention.Wo.weight.grad is not None
        
        print(f"✓ Wqkv has gradients: {qkv_has_grad}")
        print(f"✓ Wo has gradients: {wo_has_grad}")
        
        # Verify all parameters have gradients
        all_params_have_grad = h1_has_grad and qkv_has_grad and wo_has_grad
        
        if all_params_have_grad:
            print("🎉 All ROTALI parameters receive gradients correctly!")
            return True
        else:
            print("❌ Some ROTALI parameters are missing gradients!")
            return False
            
    except Exception as e:
        print(f"✗ Error in gradient test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_unpadded_rotali_gradients():
    """Test that unpadded ROTALI parameters receive gradients."""
    print("\nTesting unpadded ROTALI gradient flow...")
    
    try:
        from src.bert_layers.configuration_bert import FlexBertConfig
        from src.bert_layers.rotali_attention import FlexBertUnpadRotaliAttention
        import bert_padding
        
        config = FlexBertConfig(
            hidden_size=768,
            num_attention_heads=12,
            use_rotali=True,
            use_fa2=False,  # Disable Flash Attention for testing
            attention_probs_dropout_prob=0.0,
            padding="unpadded"
        )
        
        attention = FlexBertUnpadRotaliAttention(config, layer_id=0)
        
        # Create test input (simulate unpadded sequences)
        batch_size, max_seq_len = 2, 16
        seq_lens = [12, 14]  # Variable sequence lengths
        
        # Create padded input first
        padded_input = torch.randn(batch_size, max_seq_len, config.hidden_size, requires_grad=True)
        
        # Create attention mask
        attn_mask = torch.zeros(batch_size, max_seq_len, dtype=torch.bool)
        for i, seq_len in enumerate(seq_lens):
            attn_mask[i, :seq_len] = True
        
        # Unpad the input
        hidden_states = bert_padding.unpad_input_only(padded_input, attn_mask)
        
        # Create cumulative sequence lengths
        cu_seqlens = torch.tensor([0] + [sum(seq_lens[:i+1]) for i in range(len(seq_lens))], dtype=torch.int32)
        
        # Create indices for unpadding
        indices = torch.nonzero(attn_mask.flatten(), as_tuple=False).flatten()
        
        # Forward pass
        output = attention(
            hidden_states=hidden_states,
            cu_seqlens=cu_seqlens,
            max_seqlen=max_seq_len,
            indices=indices,
            attn_mask=attn_mask
        )
        
        # Create a simple loss
        loss = output.sum()
        
        # Backward pass
        loss.backward()
        
        # Check if h1 parameters have gradients
        h1_has_grad = attention.h1.weight.grad is not None
        h1_grad_norm = attention.h1.weight.grad.norm().item() if h1_has_grad else 0.0
        
        print(f"✓ Unpadded ROTALI h1 layer has gradients: {h1_has_grad}")
        print(f"✓ h1 gradient norm: {h1_grad_norm:.6f}")
        
        # Check other parameters too
        qkv_has_grad = attention.Wqkv.weight.grad is not None
        wo_has_grad = attention.Wo.weight.grad is not None
        
        print(f"✓ Wqkv has gradients: {qkv_has_grad}")
        print(f"✓ Wo has gradients: {wo_has_grad}")
        
        # Verify all parameters have gradients
        all_params_have_grad = h1_has_grad and qkv_has_grad and wo_has_grad
        
        if all_params_have_grad:
            print("🎉 All unpadded ROTALI parameters receive gradients correctly!")
            return True
        else:
            print("❌ Some unpadded ROTALI parameters are missing gradients!")
            return False
            
    except Exception as e:
        print(f"✗ Error in unpadded gradient test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run gradient tests."""
    print("=" * 60)
    print("ROTALI Gradient Flow Test Suite")
    print("=" * 60)
    
    test1_passed = test_rotali_gradients()
    test2_passed = test_unpadded_rotali_gradients()
    
    print("\n" + "=" * 60)
    if test1_passed and test2_passed:
        print("🎉 All gradient tests passed! ROTALI should work with DDP.")
        return 0
    else:
        print("❌ Some gradient tests failed. DDP issues may persist.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
