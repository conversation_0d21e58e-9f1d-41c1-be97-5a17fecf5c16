#!/usr/bin/env python3
"""
Test script to validate ROTALI integration with FlexBERT.

This script tests:
1. ROTALI attention layer creation
2. Configuration parameter handling
3. Basic forward pass functionality
4. Compatibility with FlexBERT architecture
"""

import sys
import os
import torch
import torch.nn as nn

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_rotali_imports():
    """Test that ROTALI classes can be imported successfully."""
    print("Testing ROTALI imports...")
    
    try:
        from src.bert_layers.rotali_attention import FlexBertRotaliAttention, FlexBertUnpadRotaliAttention
        print("✓ ROTALI attention classes imported successfully")
        return True
    except Exception as e:
        print(f"✗ Error importing ROTALI attention: {e}")
        return False

def test_fourier_imports():
    """Test that Fourier layer classes can be imported successfully."""
    print("Testing Fourier layer imports...")
    
    try:
        from src.bert_layers.fourier_layer import FlexBertFourier<PERSON>ayer, FlexBertUnpadFourierLayer
        print("✓ Fourier layer classes imported successfully")
        return True
    except Exception as e:
        print(f"✗ Error importing Fourier layer: {e}")
        return False

def test_config_creation():
    """Test FlexBertConfig creation with ROTALI parameters."""
    print("Testing FlexBertConfig with ROTALI parameters...")
    
    try:
        from src.bert_layers.configuration_bert import FlexBertConfig
        
        # Test basic config creation
        config = FlexBertConfig(
            use_rotali=True,
            rotali_fourier_frequencies=50,
            rotali_use_fourier=False,
            hidden_size=768,
            num_attention_heads=12,
            num_hidden_layers=12,
            attention_layer="rotali"
        )
        
        print(f"✓ FlexBertConfig created successfully:")
        print(f"  - use_rotali: {config.use_rotali}")
        print(f"  - rotali_fourier_frequencies: {config.rotali_fourier_frequencies}")
        print(f"  - rotali_use_fourier: {config.rotali_use_fourier}")
        print(f"  - attention_layer: {config.attention_layer}")
        
        return True, config
    except Exception as e:
        print(f"✗ Error creating FlexBertConfig: {e}")
        return False, None

def test_rotali_attention_creation():
    """Test ROTALI attention layer creation."""
    print("Testing ROTALI attention layer creation...")
    
    try:
        from src.bert_layers.configuration_bert import FlexBertConfig
        from src.bert_layers.rotali_attention import FlexBertRotaliAttention
        
        config = FlexBertConfig(
            hidden_size=768,
            num_attention_heads=12,
            use_rotali=True,
            rotali_fourier_frequencies=50
        )
        
        # Create ROTALI attention layer
        attention = FlexBertRotaliAttention(config, layer_id=0)
        
        print(f"✓ ROTALI attention layer created successfully")
        print(f"  - num_attention_heads: {attention.num_attention_heads}")
        print(f"  - attn_head_size: {attention.attn_head_size}")
        print(f"  - hidden_size: {attention.hidden_size}")
        
        return True, attention
    except Exception as e:
        print(f"✗ Error creating ROTALI attention: {e}")
        return False, None

def test_attention_factory():
    """Test attention layer factory with ROTALI."""
    print("Testing attention layer factory...")
    
    try:
        from src.bert_layers.configuration_bert import FlexBertConfig
        from src.bert_layers.attention import get_attention_layer
        
        config = FlexBertConfig(
            hidden_size=768,
            num_attention_heads=12,
            attention_layer="rotali",
            use_rotali=True,
            padding="padded"  # Test with padded version
        )
        
        # Create attention layer through factory
        attention = get_attention_layer(config, layer_id=0)
        
        print(f"✓ Attention layer created through factory")
        print(f"  - Type: {type(attention).__name__}")
        
        return True, attention
    except Exception as e:
        print(f"✗ Error creating attention through factory: {e}")
        return False, None

def test_basic_forward_pass():
    """Test basic forward pass of ROTALI attention."""
    print("Testing basic forward pass...")
    
    try:
        from src.bert_layers.configuration_bert import FlexBertConfig
        from src.bert_layers.rotali_attention import FlexBertRotaliAttention
        
        config = FlexBertConfig(
            hidden_size=768,
            num_attention_heads=12,
            use_rotali=True,
            use_fa2=False,  # Disable Flash Attention for testing
            attention_probs_dropout_prob=0.0
        )
        
        attention = FlexBertRotaliAttention(config, layer_id=0)
        attention.eval()  # Set to eval mode to disable dropout
        
        # Create test input
        batch_size, seq_len = 2, 16
        hidden_states = torch.randn(batch_size, seq_len, config.hidden_size)
        
        # Forward pass
        with torch.no_grad():
            output = attention(hidden_states)
        
        print(f"✓ Forward pass successful")
        print(f"  - Input shape: {hidden_states.shape}")
        print(f"  - Output shape: {output.shape}")
        print(f"  - Output dtype: {output.dtype}")
        
        # Check output shape
        assert output.shape == hidden_states.shape, f"Shape mismatch: {output.shape} vs {hidden_states.shape}"
        print("✓ Output shape matches input shape")
        
        return True
    except Exception as e:
        print(f"✗ Error in forward pass: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("=" * 60)
    print("ROTALI Integration Test Suite")
    print("=" * 60)
    
    tests = [
        test_rotali_imports,
        test_fourier_imports,
        test_config_creation,
        test_rotali_attention_creation,
        test_attention_factory,
        test_basic_forward_pass,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print()
        try:
            if callable(test):
                result = test()
                if isinstance(result, tuple):
                    result = result[0]  # Take first element if tuple returned
                if result:
                    passed += 1
            else:
                print(f"✗ Test {test.__name__} is not callable")
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
    
    print()
    print("=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All tests passed! ROTALI integration is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
